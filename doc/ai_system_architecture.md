# AI Multi-Agent Query Processing System Architecture

This document describes the architecture of the AI-powered multi-agent system for query processing and insight generation in the Euranet Dashboard.

## System Overview

The system follows a multi-agent architecture that processes user queries through several specialized components to generate comprehensive insights from media platform data.

## Architecture Diagram

```mermaid
graph TD
    A[User Query] --> B{QueryProcessor};
    B --> C[GemmaModel: enhance_query_understanding];
    C --> D[Data Retrieval from Database];
    D --> E[MultiAgentSystem];
    E -- Selects Agents based on Query --> F(TrendAgent);
    E -- Selects Agents based on Query --> G(ComparisonAgent);
    E -- Selects Agents based on Query --> H(AnomalyAgent);
    F -- Generates Insights uses Gemma --> I[Raw Insights];
    G -- Generates Insights uses Gemma --> I;
    H -- Generates Insights uses Gemma --> I;
    I --> J[MultiAgentSystem: Synthesize Insights uses Gemma];
    J --> K[InsightAnalyzer: Post-process & Rank];
    K --> L[Display Insights to User];

    subgraph Agents
        F
        G
        H
    end

    subgraph "Insight Processing"
        I
        J
        K
    end
```

## Component Descriptions

### 1. User Query Processing
- **User Query**: Natural language input from the user
- **QueryProcessor**: Analyzes and categorizes the user's query
- **GemmaModel**: Enhances query understanding using the Gemma 3:1b-it model

### 2. Data Layer
- **Data Retrieval**: Fetches relevant data from the SQLite database containing unified media platform metrics

### 3. Multi-Agent System
The system employs specialized agents for different types of analysis:

#### TrendAgent
- Analyzes temporal patterns and trends in the data
- Identifies growth patterns, seasonal variations, and long-term trends

#### ComparisonAgent
- Performs comparative analysis between different platforms, time periods, or metrics
- Generates insights about relative performance and benchmarking

#### AnomalyAgent
- Detects unusual patterns, outliers, and anomalies in the data
- Identifies significant deviations from expected behavior

### 4. Insight Processing Pipeline
- **Raw Insights**: Initial insights generated by individual agents
- **Insight Synthesis**: Combines and synthesizes insights from multiple agents using Gemma
- **Post-processing & Ranking**: Analyzes, ranks, and formats insights for presentation

### 5. Output
- **Display Insights**: Final processed insights presented to the user through the dashboard interface

## Key Features

1. **AI-Powered Understanding**: Uses Gemma 3:1b-it model throughout the pipeline for enhanced natural language processing
2. **Specialized Analysis**: Different agents handle specific types of analysis (trends, comparisons, anomalies)
3. **Intelligent Synthesis**: Combines insights from multiple agents for comprehensive analysis
4. **Adaptive Processing**: System selects appropriate agents based on query type and context
5. **Ranked Output**: Insights are processed, ranked, and presented in order of relevance and importance

## Technology Stack

- **LLM**: Gemma 3:1b-it model for natural language understanding and insight generation
- **Database**: SQLite with unified media platform data
- **Architecture**: Multi-agent system with specialized analysis components
- **Framework**: Python-based implementation with Dash for web interface

## Data Flow

1. User submits a natural language query
2. QueryProcessor analyzes the query intent and context
3. Gemma model enhances query understanding
4. Relevant data is retrieved from the database
5. MultiAgentSystem selects appropriate agents based on query type
6. Selected agents generate specialized insights using Gemma
7. Raw insights are synthesized and combined
8. InsightAnalyzer post-processes and ranks the insights
9. Final insights are displayed to the user

This architecture enables the system to provide intelligent, contextual, and comprehensive analysis of media platform data through natural language interaction.
